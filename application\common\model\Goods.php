<?php
// +----------------------------------------------------------------------
// | likeshop开源商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop系列产品在gitee、github等公开渠道开源版本可免费商用，未经许可不能去除前后端官方版权标识
// |  likeshop系列产品收费版本务必购买商业授权，购买去版权授权后，方可去除前后端官方版权标识
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | likeshop团队版权所有并拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshop.cn.team
// +----------------------------------------------------------------------

namespace app\common\model;

use app\common\server\UrlServer;
use think\Model;

class Goods extends Model
{
    const STATUS_RECYCLE = -1; // 回收站
    const STATUS_STORAGE = 0; // 下架(仓库中)
    const STATUS_SELL = 1; // 上架 (销售中)

    /**
     * @notes 获取商品状态
     * @param $state
     * @return string|string[]
     * <AUTHOR>
     * @date 2022/1/12 10:384
     */
    public static function getStatusDesc($state)
    {
        $data = [
            self::STATUS_SELL => '上架',
            self::STATUS_STORAGE => '下架',
            self::STATUS_RECYCLE => '回收站',
        ];
        if ($state === true) {
            return $data;
        }
        return $data[$state] ?? '';
    }

    /**
     * 关联动态模板
     * @return \think\model
elation\BelongsTo
     */
    public function dynamicTemplate()
    {
        return $this->belongsTo('DynamicTemplate', 'dynamic_template_id', 'id');
    }

    /**
     * 获取器 - 模板参数配置
     * @param $value
     * @param $data
     * @return array
     */
    public function getTemplateParamsConfigAttr($value, $data)
    {
        return $value ? json_decode($value, true) : [];
    }

    /**
     * 修改器 - 模板参数配置
     * @param $value
     * @param $data
     * @return string
     */
    public function setTemplateParamsConfigAttr($value, $data)
    {
        return is_array($value) ? json_encode($value, JSON_UNESCAPED_UNICODE) : $value;
    }

    /**
     * 获取器 - 是否启用模板功能文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getEnableTemplateTextAttr($value, $data)
    {
        return $data['enable_template'] ? '已启用' : '未启用';
    }

    /**
     * 检查商品是否启用了模板功能
     * @return bool
     */
    public function hasTemplate()
    {
        return $this->enable_template && $this->dynamic_template_id > 0;
    }

    /**
     * 获取商品的模板信息
     * @return array|null
     */
    public function getTemplateInfo()
    {
        if (!$this->hasTemplate()) {
            return null;
        }

        $template = $this->dynamicTemplate;
        if (!$template) {
            return null;
        }

        // 获取详细的参数信息
        $params = [];
        if ($template->params) {
            foreach ($template->params as $param) {
                $params[] = [
                    'id' => $param->id,
                    'param_name' => $param->param_name,
                    'param_label' => $param->param_label,
                    'param_type' => $param->param_type,
                    'param_type_text' => $this->getParamTypeText($param->param_type),
                    'default_value' => $param->default_value,
                    'is_required' => $param->is_required,
                    'max_length' => $param->max_length,
                    'param_description' => $param->param_description,
                    'original_text' => $param->original_text,
                    'element_uuid' => $param->element_uuid,
                    'sort' => $param->sort,
                    'status' => $param->status
                ];
            }
        }

        return [
            'template_id' => $template->template_id,
            'title' => $template->title,
            'description' => $template->description,
            'thumbnail' => $template->thumbnail,
            'width' => $template->width,
            'height' => $template->height,
            'param_count' => $template->param_count,
            'params_config' => $this->template_params_config,
            'params' => $params
        ];
    }

    /**
     * 获取参数类型文本
     * @param int $type
     * @return string
     */
    private function getParamTypeText($type)
    {
        $types = [
            1 => '文本',
            2 => '数字',
            3 => '图片',
            4 => '颜色',
            5 => '日期',
            6 => '下拉选择'
        ];
        return $types[$type] ?? '未知';
    }

}