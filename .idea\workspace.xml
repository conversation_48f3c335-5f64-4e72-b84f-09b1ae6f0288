<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="b0f5b031-5d22-4c39-b9d9-9f8a706f9072" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/application/api/controller/Goods.php" beforeDir="false" afterPath="$PROJECT_DIR$/application/api/controller/Goods.php" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/application/api/logic/GoodsLogic.php" beforeDir="false" afterPath="$PROJECT_DIR$/application/api/logic/GoodsLogic.php" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/application/common/model/Goods.php" beforeDir="false" afterPath="$PROJECT_DIR$/application/common/model/Goods.php" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ComposerConfigs">
    <option name="configs">
      <option value="$PROJECT_DIR$/thinkphp/composer.json" />
    </option>
  </component>
  <component name="ComposerSettings" synchronizationState="SYNCHRONIZE">
    <pharConfigPath>$PROJECT_DIR$/composer.json</pharConfigPath>
    <execution />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="PhpWorkspaceProjectConfiguration" interpreter_name="D:\phpstudy_pro\Extensions\php\php7.4.3nts\php.exe">
    <include_path>
      <path value="$PROJECT_DIR$/vendor/overtrue/wechat" />
      <path value="$PROJECT_DIR$/vendor/overtrue/socialite" />
      <path value="$PROJECT_DIR$/vendor/danielstjules/stringy" />
      <path value="$PROJECT_DIR$/vendor/qcloud/cos-sdk-v5" />
      <path value="$PROJECT_DIR$/vendor/adbario/php-dot-notation" />
      <path value="$PROJECT_DIR$/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/vendor/symfony/service-contracts" />
      <path value="$PROJECT_DIR$/vendor/psr/cache" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-ctype" />
      <path value="$PROJECT_DIR$/vendor/psr/http-client" />
      <path value="$PROJECT_DIR$/vendor/pimple/pimple" />
      <path value="$PROJECT_DIR$/vendor/rmccue/requests" />
      <path value="$PROJECT_DIR$/vendor/symfony/event-dispatcher-contracts" />
      <path value="$PROJECT_DIR$/vendor/psr/http-message" />
      <path value="$PROJECT_DIR$/vendor/symfony/mime" />
      <path value="$PROJECT_DIR$/vendor/symfony/yaml" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-php73" />
      <path value="$PROJECT_DIR$/vendor/psr/log" />
      <path value="$PROJECT_DIR$/vendor/symfony/property-access" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-php72" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-intl-idn" />
      <path value="$PROJECT_DIR$/vendor/endroid/qr-code" />
      <path value="$PROJECT_DIR$/vendor/dragonmantank/cron-expression" />
      <path value="$PROJECT_DIR$/vendor/yly-openapi/yly-openapi-sdk" />
      <path value="$PROJECT_DIR$/vendor/monolog/monolog" />
      <path value="$PROJECT_DIR$/vendor/aliyuncs/oss-sdk-php" />
      <path value="$PROJECT_DIR$/vendor/mtdowling/jmespath.php" />
      <path value="$PROJECT_DIR$/vendor/bacon/bacon-qr-code" />
      <path value="$PROJECT_DIR$/vendor/topthink/think-installer" />
      <path value="$PROJECT_DIR$/vendor/topthink/think-image" />
      <path value="$PROJECT_DIR$/vendor/topthink/think-helper" />
      <path value="$PROJECT_DIR$/vendor/topthink/think-captcha" />
      <path value="$PROJECT_DIR$/vendor/clagiordano/weblibs-configmanager" />
      <path value="$PROJECT_DIR$/vendor/topthink/framework" />
      <path value="$PROJECT_DIR$/vendor/songshenzong/support" />
      <path value="$PROJECT_DIR$/vendor/easywechat-composer/easywechat-composer" />
      <path value="$PROJECT_DIR$/vendor/alibabacloud/client" />
      <path value="$PROJECT_DIR$/vendor/alibabacloud/tea" />
      <path value="$PROJECT_DIR$/vendor/alibabacloud/tea-fileform" />
      <path value="$PROJECT_DIR$/vendor/alipaysdk/easysdk" />
      <path value="$PROJECT_DIR$/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-mbstring" />
      <path value="$PROJECT_DIR$/vendor/symfony/deprecation-contracts" />
      <path value="$PROJECT_DIR$/vendor/symfony/cache-contracts" />
      <path value="$PROJECT_DIR$/vendor/symfony/http-foundation" />
      <path value="$PROJECT_DIR$/vendor/guzzlehttp/guzzle-services" />
      <path value="$PROJECT_DIR$/vendor/symfony/event-dispatcher" />
      <path value="$PROJECT_DIR$/vendor/guzzlehttp/uri-template" />
      <path value="$PROJECT_DIR$/vendor/symfony/options-resolver" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-intl-normalizer" />
      <path value="$PROJECT_DIR$/vendor/psr/http-factory" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-php80" />
      <path value="$PROJECT_DIR$/vendor/guzzlehttp/promises" />
      <path value="$PROJECT_DIR$/vendor/khanamiryan/qrcode-detector-decoder" />
      <path value="$PROJECT_DIR$/vendor/psr/container" />
      <path value="$PROJECT_DIR$/vendor/symfony/psr-http-message-bridge" />
      <path value="$PROJECT_DIR$/vendor/guzzlehttp/command" />
      <path value="$PROJECT_DIR$/vendor/symfony/cache" />
      <path value="$PROJECT_DIR$/vendor/guzzlehttp/psr7" />
      <path value="$PROJECT_DIR$/vendor/psr/simple-cache" />
      <path value="$PROJECT_DIR$/vendor/symfony/var-exporter" />
      <path value="$PROJECT_DIR$/vendor/guzzlehttp/guzzle" />
      <path value="$PROJECT_DIR$/vendor/composer" />
      <path value="$PROJECT_DIR$/vendor/tencentcloud/tencentcloud-sdk-php" />
      <path value="$PROJECT_DIR$/vendor/ralouphie/getallheaders" />
      <path value="$PROJECT_DIR$/vendor/myclabs/php-enum" />
      <path value="$PROJECT_DIR$/vendor/qiniu/php-sdk" />
    </include_path>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="328r4Xax5iaGa8UR8TX7Z1wEOCd" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "git-widget-placeholder": "master",
    "kotlin-language-version-configured": "true",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "com.intellij.jarRepository.settings.RemoteRepositoriesConfigurable",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager">
    <configuration default="true" type="ComposerRunConfigurationType" factoryName="Composer Script">
      <option name="pathToComposerJson" value="$PROJECT_DIR$/composer.json" />
      <method v="2" />
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9f38398b9061-18abd8497189-intellij.indexing.shared.core-IU-241.14494.240" />
        <option value="bundled-js-predefined-1d06a55b98c1-74d2a5396914-JavaScript-IU-241.14494.240" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="b0f5b031-5d22-4c39-b9d9-9f8a706f9072" name="Changes" comment="" />
      <created>1750991538607</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1750991538607</updated>
      <workItem from="1756812853520" duration="2451000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>