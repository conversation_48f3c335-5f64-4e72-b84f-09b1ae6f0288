<?php
// +----------------------------------------------------------------------
// | likeshop开源商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop系列产品在gitee、github等公开渠道开源版本可免费商用，未经许可不能去除前后端官方版权标识
// |  likeshop系列产品收费版本务必购买商业授权，购买去版权授权后，方可去除前后端官方版权标识
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | likeshop团队版权所有并拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshop.cn.team
// +----------------------------------------------------------------------

namespace app\admin\controller;

use app\admin\logic\DynamicTemplateLogic;
use app\admin\validate\DynamicTemplate as DynamicTemplateValidate;
use app\common\logic\XunpaiLogic;

/**
 * 动态模板管理控制器
 * Class DynamicTemplate
 * @package app\admin\controller
 */
class DynamicTemplate extends AdminBase
{
    /**
     * 模板列表
     * @return mixed
     */
    public function lists()
    {
        if ($this->request->isAjax()) {
            $get = $this->request->get();
            $lists = DynamicTemplateLogic::lists($get);
            $this->_success('', $lists);
        }

        $this->assign('category_options', DynamicTemplateLogic::getCategoryOptions());
        $this->assign('status_options', DynamicTemplateLogic::getStatusOptions());
        return $this->fetch();
    }

    /**
     * 添加模板
     * @return mixed
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $post = $this->request->post();

            $result = DynamicTemplateLogic::add($post);
            if ($result === true) {
                $this->_success('添加成功', [], 1, 0, 3, url('lists'));
            } else {
                $this->_error($result);
            }
        }
        
        $this->assign('category_options', DynamicTemplateLogic::getCategoryOptions());
        $this->assign('param_type_options', DynamicTemplateLogic::getParamTypeOptions());
        return $this->fetch();
    }

    /**
     * 编辑模板
     * @return mixed
     */
    public function edit()
    {
        $id = $this->request->get('id', 0);
        
        if ($this->request->isPost()) {
            $post = $this->request->post();
            $post['id'] = $post['id'] ? $post['id'] : $id;
            
            $result = DynamicTemplateLogic::edit($post);
            if ($result === true) {
                $this->_success('编辑成功', [], 1, 0, 3, url('lists'));
            } else {
                $this->_error($result);
            }
        }
        
        $info = DynamicTemplateLogic::info($id);
        if (!$info) {
            $this->_error('模板不存在');
        }
        
        $this->assign('info', $info);
        $this->assign('category_options', DynamicTemplateLogic::getCategoryOptions());
        $this->assign('param_type_options', DynamicTemplateLogic::getParamTypeOptions());
        return $this->fetch();
    }

    /**
     * 删除模板
     * @return mixed
     */
    public function del()
    {
        $id = $this->request->post('id');
        
        $validate = new DynamicTemplateValidate();
        if (!$validate->scene('del')->check(['id' => $id])) {
            $this->_error($validate->getError());
        }
        
        $result = DynamicTemplateLogic::del([$id]);
        if ($result === true) {
            $this->_success('删除成功');
        } else {
            $this->_error($result);
        }
    }

    /**
     * 批量删除
     * @return mixed
     */
    public function delAll()
    {
        $ids = $this->request->post('ids');
        if (empty($ids)) {
            $this->_error('请选择要删除的数据');
        }
        
        $result = DynamicTemplateLogic::del($ids);
        if ($result === true) {
            $this->_success('删除成功');
        } else {
            $this->_error($result);
        }
    }

    /**
     * 切换状态
     * @return mixed
     */
    public function status()
    {
        $post = $this->request->post();
        
        $validate = new DynamicTemplateValidate();
        if (!$validate->scene('status')->check($post)) {
            $this->_error($validate->getError());
        }
        
        $result = DynamicTemplateLogic::status($post['id'], $post['status']);
        if ($result === true) {
            $this->_success('操作成功');
        } else {
            $this->_error($result);
        }
    }

    /**
     * 模板详情
     * @return mixed
     */
    public function info()
    {
        $id = $this->request->get('id', 0);
        $info = DynamicTemplateLogic::info($id);
        
        if (!$info) {
            $this->_error('模板不存在');
        }
        
        $this->assign('info', $info);
        return $this->fetch();
    }

    /**
     * 模板预览
     * @return mixed
     */
    public function preview()
    {
        $id = $this->request->get('id', 0);
        
        if ($this->request->isPost()) {
            $params = $this->request->post('params', []);
            $result = DynamicTemplateLogic::preview($id, $params);
            
            if (is_array($result) && $result['code'] == 1) {
                $this->_success('预览生成成功', $result['data']);
            } else {
                $this->_error(is_string($result) ? $result : '预览生成失败');
            }
        }
        
        $info = DynamicTemplateLogic::info($id);
        if (!$info) {
            $this->_error('模板不存在');
        }
        
        $this->assign('info', $info);
        return $this->fetch();
    }

    /**
     * 批量生成
     * @return mixed
     */
    public function batch()
    {
        $id = $this->request->get('id', 0);
        
        if ($this->request->isPost()) {
            $post = $this->request->post();
            $post['id'] = $id;
            
            $validate = new DynamicTemplateValidate();
            if (!$validate->scene('batch')->check($post)) {
                $this->_error($validate->getError());
            }
            
            $result = DynamicTemplateLogic::createBatchTask($post, $this->admin_id);
            if (is_array($result)) {
                $this->_success('批量任务创建成功', $result, url('batch_lists'));
            } else {
                $this->_error($result);
            }
        }
        
        $info = DynamicTemplateLogic::info($id);
        if (!$info) {
            $this->_error('模板不存在');
        }
        
        $this->assign('info', $info);
        return $this->fetch();
    }

    /**
     * 批量任务列表
     * @return mixed
     */
    public function batch_lists()
    {
        if ($this->request->isAjax()) {
            $get = $this->request->get();
            $lists = DynamicTemplateLogic::batch_lists($get);
            $this->_success('', $lists);
        }

        return $this->fetch();
    }

    /**
     * 批量任务详情
     * @return mixed
     */
    public function batchInfo()
    {
        $batchId = $this->request->get('batch_id', '');
        $info = DynamicTemplateLogic::batchInfo($batchId);
        
        if (!$info) {
            $this->_error('批量任务不存在');
        }
        
        if ($this->request->isAjax()) {
            $this->_success('获取成功', $info);
        }
        
        $this->assign('info', $info);
        return $this->fetch();
    }

    /**
     * 获取迅排模板列表
     * @return mixed
     */
    public function gettemplates()
    {
        try {
            // 调用迅排API获取模板列表
            $result = XunpaiLogic::getTemplateList([
                'page' => 1,
                'pageSize' => 50
            ]);

            if ($result['code'] == 1) {
                // 处理返回的数据结构
                $data = $result['data'];
                $templates = [];

                // 根据实际返回的数据结构处理
                if (is_array($data) && isset($data['list']) && is_array($data['list'])) {
                    // 处理 {data: {list: [...], total: x}} 格式
                    foreach ($data['list'] as $template) {
                        $templates[] = [
                            'id' => $template['id'],
                            'title' => $template['title'],
                            'category' => $template['category'] ?? 'poster',
                            'thumbnail' => $template['thumbnail'] ?? ''
                        ];
                    }
                } elseif (is_array($data) && isset($data['templates'])) {
                    // 处理 {data: {templates: [...]}} 格式
                    foreach ($data['templates'] as $template) {
                        $templates[] = [
                            'id' => $template['id'],
                            'title' => $template['title'],
                            'category' => $template['category'] ?? 'poster',
                            'thumbnail' => $template['thumbnail'] ?? ''
                        ];
                    }
                } elseif (is_array($data) && !empty($data)) {
                    // 处理 {data: [...]} 格式
                    foreach ($data as $template) {
                        if (isset($template['id']) && isset($template['title'])) {
                            $templates[] = [
                                'id' => $template['id'],
                                'title' => $template['title'],
                                'category' => $template['category'] ?? 'poster',
                                'thumbnail' => $template['thumbnail'] ?? ''
                            ];
                        }
                    }
                }

                $this->_success('获取成功', $templates);
            } else {
                $this->_error('获取模板列表失败：' . ($result['msg'] ?? '未知错误'));
            }
        } catch (\think\exception\HttpResponseException $e) {
            // HttpResponseException是ThinkPHP的正常响应机制，需要重新抛出
            throw $e;
        } catch (\Exception $e) {
            $this->_error('获取模板列表失败：' . $e->getMessage());
        }
    }

    /**
     * 获取模板详情
     * @return mixed
     */
    public function gettemplate()
    {
        $templateId = $this->request->get('id', '');
        if (empty($templateId)) {
            $this->_error('模板ID不能为空');
        }

        try {
            // 调用迅排API获取模板详情
            $result = XunpaiLogic::getTemplateDetail($templateId);

            if ($result['code'] == 1 && !empty($result['data'])) {
                $template = $result['data'];
                $this->_success('获取成功', $template);
            } else {
                $this->_error('获取模板详情失败：' . ($result['msg'] ?? '未知错误'));
            }
        } catch (\think\exception\HttpResponseException $e) {
            // HttpResponseException是ThinkPHP的正常响应机制，需要重新抛出
            throw $e;
        } catch (\Exception $e) {
            $this->_error('获取模板详情失败：' . $e->getMessage());
        }
    }

    /**
     * 解析模板参数
     * @return mixed
     */
    public function parsetemplate()
    {
        $templateId = $this->request->post('templateId', '');
        if (empty($templateId)) {
            $this->_error('模板ID不能为空');
        }

        try {
            // 调用迅排API解析模板参数
            $result = XunpaiLogic::parseTemplateParameters($templateId);

            if ($result['code'] == 1) {
                $this->_success('解析成功', $result['data']);
            } else {
                $this->_error('解析模板参数失败：' . ($result['msg'] ?? '未知错误'));
            }
        } catch (\think\exception\HttpResponseException $e) {
            // HttpResponseException是ThinkPHP的正常响应机制，需要重新抛出
            throw $e;
        } catch (\Exception $e) {
            $this->_error('解析模板参数失败：' . $e->getMessage());
        }
    }


}
