<?php
// +----------------------------------------------------------------------
// | likeshop开源商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop系列产品在gitee、github等公开渠道开源版本可免费商用，未经许可不能去除前后端官方版权标识
// |  likeshop系列产品收费版本务必购买商业授权，购买去版权授权后，方可去除前后端官方版权标识
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | likeshop团队版权所有并拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshop.cn.team
// +----------------------------------------------------------------------

namespace app\admin\logic;

use app\common\logic\XunpaiLogic;
use app\common\model\DynamicTemplate;
use app\common\model\DynamicTemplateParams;
use app\common\model\DynamicTemplateBatch;
use think\Db;
use think\Exception;

/**
 * 动态模板业务逻辑
 * Class DynamicTemplateLogic
 * @package app\admin\logic
 */
class DynamicTemplateLogic
{
    /**
     * 获取模板列表
     * @param array $get
     * @return array
     */
    public static function lists($get)
    {
        $where = [];
        
        // 状态筛选
        if (isset($get['status']) && $get['status'] !== '') {
            $where[] = ['status', '=', $get['status']];
        }
        
        // 分类筛选
        if (!empty($get['category'])) {
            $where[] = ['category', '=', $get['category']];
        }
        
        // 关键词搜索
        if (!empty($get['keyword'])) {
            $where[] = ['title|description', 'like', '%' . $get['keyword'] . '%'];
        }
        
        $page = $get['page'] ?? 1;
        $limit = $get['limit'] ?? 15;
        
        $count = DynamicTemplate::where($where)->count();
        $lists = DynamicTemplate::where($where)
            ->field('id,template_id,title,description,category,tags,thumbnail,width,height,text_elements_count,param_count,status,sort,create_time,update_time')
            ->order('sort desc, id desc')
            ->page($page, $limit)
            ->select();
        
        foreach ($lists as &$item) {
            $item['category_text'] = $item->category_text;
            $item['status_text'] = $item->status_text;

            // 由于模型开启了自动时间戳，时间字段已经被转换为日期格式
            // 直接使用已格式化的时间，或者重新获取原始时间戳
            $item['create_time_text'] = $item['create_time'];
            $item['update_time_text'] = $item['update_time'];
        }
        
        return ['count' => $count, 'lists' => $lists];
    }

    /**
     * 获取模板详情
     * @param int $id
     * @return array|null
     */
    public static function info($id)
    {
        $info = DynamicTemplate::with(['params'])->find($id);
        if (!$info) {
            return null;
        }
        
        $info['category_text'] = $info->category_text;
        $info['status_text'] = $info->status_text;

        // 由于模型开启了自动时间戳，时间字段已经被转换为日期格式
        $info['create_time_text'] = $info['create_time'];
        $info['update_time_text'] = $info['update_time'];
        
        return $info->toArray();
    }

    /**
     * 添加模板
     * @param array $post
     * @return bool|string
     */
    public static function add($post)
    {
        Db::startTrans();
        try {
            $data = [
                'template_id'         => $post['template_id'],
                'title'               => $post['title'],
                'description'         => $post['description'] ?? '',
                'category'            => $post['category'],
                'tags'                => is_array($post['tags']) ? implode(',', $post['tags']) : $post['tags'],
                'thumbnail'           => $post['thumbnail'] ?? '',
                'width'               => $post['width'],
                'height'              => $post['height'],
                'text_elements_count' => $post['text_elements_count'] ?? 0,
                'param_count'         => $post['param_count'] ?? 0,
                'template_data'       => is_array($post['template_data']) ? json_encode($post['template_data'], JSON_UNESCAPED_UNICODE) : $post['template_data'],
                'status'              => $post['status'] ?? 1,
                'sort'                => $post['sort'] ?? 50,
            ];
            
            $template = DynamicTemplate::create($data);
            
            // 如果有参数配置，保存参数
            if (!empty($post['params']) && is_array($post['params'])) {
                self::saveParams($template->id, $post['params']);
            }
            
            Db::commit();
            return true;
        } catch (Exception $e) {
            Db::rollback();

            // 优化错误提示
            $errorMsg = $e->getMessage();
            if (strpos($errorMsg, 'Duplicate entry') !== false && strpos($errorMsg, 'uk_template_id') !== false) {
                return '该模板已经添加过了，请选择其他模板或删除已有模板后重新添加';
            }

            return $errorMsg;
        }
    }

    /**
     * 编辑模板
     * @param array $post
     * @return bool|string
     */
    public static function edit($post)
    {
        Db::startTrans();
        try {
            $template = DynamicTemplate::find($post['id']);
            if (!$template) {
                return '模板不存在';
            }
            
            $data = [
                'template_id'         => $post['template_id'],
                'title'               => $post['title'],
                'description'         => $post['description'] ?? '',
                'category'            => $post['category'],
                'tags'                => is_array($post['tags']) ? implode(',', $post['tags']) : $post['tags'],
                'thumbnail'           => $post['thumbnail'] ?? '',
                'width'               => $post['width'],
                'height'              => $post['height'],
                'text_elements_count' => $post['text_elements_count'] ?? 0,
                'param_count'         => $post['param_count'] ?? 0,
                'template_data'       => is_array($post['template_data']) ? json_encode($post['template_data'], JSON_UNESCAPED_UNICODE) : $post['template_data'],
                'status'              => $post['status'] ?? 1,
                'sort'                => $post['sort'] ?? 50,
            ];
            
            $template->save($data);
            
            // 如果有参数配置，更新参数
            if (!empty($post['params']) && is_array($post['params'])) {
                // 删除原有参数
                DynamicTemplateParams::where('template_id', $template->id)->delete();
                // 保存新参数
                self::saveParams($template->id, $post['params']);
            }
            
            Db::commit();
            return true;
        } catch (Exception $e) {
            Db::rollback();
            return $e->getMessage();
        }
    }

    /**
     * 删除模板
     * @param array $ids
     * @return bool|string
     */
    public static function del($ids)
    {
        Db::startTrans();
        try {
            // 检查是否有正在进行的批量任务
            $runningBatches = DynamicTemplateBatch::where('template_id', 'in', $ids)
                ->where('status', 'in', [DynamicTemplateBatch::STATUS_PENDING, DynamicTemplateBatch::STATUS_PROCESSING])
                ->count();
            
            if ($runningBatches > 0) {
                return '存在正在进行的批量任务，无法删除';
            }
            
            // 软删除模板
            DynamicTemplate::destroy($ids);
            
            // 删除相关参数配置
            DynamicTemplateParams::where('template_id', 'in', $ids)->delete();
            
            Db::commit();
            return true;
        } catch (Exception $e) {
            Db::rollback();
            return $e->getMessage();
        }
    }

    /**
     * 切换状态
     * @param int $id
     * @param int $status
     * @return bool|string
     */
    public static function status($id, $status)
    {
        $template = DynamicTemplate::find($id);
        if (!$template) {
            return '模板不存在';
        }
        
        $template->status = $status;
        $result = $template->save();
        
        return $result ? true : '操作失败';
    }

    /**
     * 保存参数配置
     * @param int $templateId
     * @param array $params
     * @return bool
     */
    private static function saveParams($templateId, $params)
    {
        $data = [];
        foreach ($params as $index => $param) {
            // 验证必填字段
            if (empty($param['param_name']) || empty($param['param_label'])) {
                throw new Exception('参数名称和参数标签不能为空');
            }

            $data[] = [
                'template_id'       => $templateId,
                'element_uuid'      => $param['element_uuid'] ?? '',
                'param_name'        => $param['param_name'],
                'param_label'       => $param['param_label'],
                'param_description' => $param['param_description'] ?? '',
                'param_type'        => $param['param_type'] ?? 'text',
                'original_text'     => $param['original_text'] ?? '',
                'default_value'     => $param['default_value'] ?? '',
                'max_length'        => $param['max_length'] ?? 100,
                'is_required'       => $param['is_required'] ?? 0,
                'sort'              => $param['sort'] ?? ($index + 1) * 10,
                'status'            => 1,
                'create_time'       => time(),
                'update_time'       => time(),
            ];
        }

        if (!empty($data)) {
            return Db::name('dynamic_template_params')->insertAll($data);
        }

        return true;
    }



    /**
     * 获取模板预览
     * @param int $id
     * @param array $params
     * @return array|string
     */
    public static function preview($id, $params = [])
    {
        try {
            $template = DynamicTemplate::find($id);
            if (!$template) {
                return '模板不存在';
            }

            if (empty($params)) {
                // 使用默认参数
                $templateParams = DynamicTemplateParams::where('template_id', $id)
                    ->where('status', 1)
                    ->select();

                $params = [];
                foreach ($templateParams as $param) {
                    $params[$param['param_name']] = $param['default_value'];
                }
            }

            // 调用迅排API生成预览
            $result = XunpaiLogic::generateParameterPreview($template['template_id'], $params);

            return $result;

        } catch (Exception $e) {
            return '预览生成失败：' . $e->getMessage();
        }
    }

    /**
     * 创建批量生成任务
     * @param array $post
     * @param int $adminId
     * @return bool|string
     */
    public static function createBatchTask($post, $adminId)
    {
        try {
            $template = DynamicTemplate::find($post['id']);
            if (!$template) {
                return '模板不存在';
            }

            $batchId = DynamicTemplateBatch::generateBatchId();

            $outputOptions = [
                'width' => $post['output_width'] ?? $template['width'],
                'height' => $post['output_height'] ?? $template['height'],
                'quality' => $post['output_quality'] ?? 0.9,
                'type' => 'file',
            ];

            $data = [
                'batch_id'        => $batchId,
                'template_id'     => $template['id'],
                'task_name'       => $post['task_name'],
                'total_items'     => count($post['parameters_list']),
                'completed_items' => 0,
                'failed_items'    => 0,
                'progress'        => 0,
                'status'          => DynamicTemplateBatch::STATUS_PENDING,
                'parameters_data' => json_encode($post['parameters_list'], JSON_UNESCAPED_UNICODE),
                'output_options'  => json_encode($outputOptions, JSON_UNESCAPED_UNICODE),
                'admin_id'        => $adminId,
            ];

            $batch = DynamicTemplateBatch::create($data);

            // 调用迅排API创建批量任务
            $result = XunpaiLogic::createBatchGenerate(
                $template['template_id'],
                $post['parameters_list'],
                $outputOptions
            );

            if ($result['code'] == 1) {
                // 更新批量任务状态
                $batch->save([
                    'status' => DynamicTemplateBatch::STATUS_PROCESSING,
                    'result_data' => json_encode(['xunpai_batch_id' => $result['data']['batchId']], JSON_UNESCAPED_UNICODE),
                ]);

                return ['batch_id' => $batchId, 'xunpai_batch_id' => $result['data']['batchId']];
            } else {
                $batch->save([
                    'status' => DynamicTemplateBatch::STATUS_FAILED,
                    'error_message' => $result['msg'],
                ]);
                return '批量任务创建失败：' . $result['msg'];
            }

        } catch (Exception $e) {
            return '批量任务创建失败：' . $e->getMessage();
        }
    }

    /**
     * 获取批量任务列表
     * @param array $get
     * @return array
     */
    public static function batch_lists($get)
    {
        $where = [];

        // 状态筛选
        if (isset($get['status']) && $get['status'] !== '') {
            $where[] = ['status', '=', $get['status']];
        }

        // 模板筛选
        if (!empty($get['template_id'])) {
            $where[] = ['template_id', '=', $get['template_id']];
        }

        $page = $get['page'] ?? 1;
        $limit = $get['limit'] ?? 15;

        $count = DynamicTemplateBatch::where($where)->count();
        $lists = DynamicTemplateBatch::with(['template'])
            ->where($where)
            ->field('id,batch_id,template_id,task_name,total_items,completed_items,failed_items,progress,status,admin_id,create_time,update_time,complete_time')
            ->order('id desc')
            ->page($page, $limit)
            ->select();

        foreach ($lists as &$item) {
            $item['status_text'] = $item->status_text;
            $item['status_color'] = $item->status_color;
            $item['template_title'] = $item->template ? $item->template['title'] : '未知模板';

            // 获取管理员信息
            $adminInfo = $item->getAdminInfo();
            $item['admin_name'] = $adminInfo ? $adminInfo['name'] : '未知管理员';

            // 由于模型开启了自动时间戳，时间字段已经被转换为日期格式
            $item['create_time_text'] = $item['create_time'];
            $item['complete_time_text'] = $item['complete_time'] ?: '';
        }

        return ['count' => $count, 'lists' => $lists];
    }

    /**
     * 获取批量任务详情
     * @param string $batchId
     * @return array|null
     */
    public static function batchInfo($batchId)
    {
        $batch = DynamicTemplateBatch::with(['template'])
            ->where('batch_id', $batchId)
            ->find();

        if (!$batch) {
            return null;
        }

        // 从迅排API获取最新状态
        $resultData = $batch['result_data'];
        if (!empty($resultData['xunpai_batch_id'])) {
            $statusResult = XunpaiLogic::getBatchStatus($resultData['xunpai_batch_id']);
            if ($statusResult['code'] == 1) {
                $xunpaiData = $statusResult['data'];

                // 更新本地状态
                $status = DynamicTemplateBatch::STATUS_PROCESSING;
                if ($xunpaiData['status'] == 'completed') {
                    $status = DynamicTemplateBatch::STATUS_COMPLETED;
                } elseif ($xunpaiData['status'] == 'failed') {
                    $status = DynamicTemplateBatch::STATUS_FAILED;
                }

                $batch->save([
                    'completed_items' => $xunpaiData['completedItems'],
                    'failed_items' => $xunpaiData['failedItems'],
                    'progress' => $xunpaiData['progress'],
                    'status' => $status,
                    'result_data' => json_encode(array_merge($resultData, $xunpaiData), JSON_UNESCAPED_UNICODE),
                    'complete_time' => $status >= DynamicTemplateBatch::STATUS_COMPLETED ? time() : null,
                ]);

                $batch->refresh();
            }
        }

        $batch['status_text'] = $batch->status_text;
        $batch['status_color'] = $batch->status_color;
        $batch['template_title'] = $batch->template ? $batch->template['title'] : '未知模板';
        // 由于模型开启了自动时间戳，时间字段已经被转换为日期格式
        $batch['create_time_text'] = $batch['create_time'];
        $batch['complete_time_text'] = $batch['complete_time'] ?: '';

        return $batch->toArray();
    }

    /**
     * 获取分类选项
     * @return array
     */
    public static function getCategoryOptions()
    {
        return DynamicTemplate::getCategoryOptions();
    }

    /**
     * 获取状态选项
     * @return array
     */
    public static function getStatusOptions()
    {
        return DynamicTemplate::getStatusOptions();
    }

    /**
     * 获取参数类型选项
     * @return array
     */
    public static function getParamTypeOptions()
    {
        return DynamicTemplateParams::getParamTypeOptions();
    }
}
