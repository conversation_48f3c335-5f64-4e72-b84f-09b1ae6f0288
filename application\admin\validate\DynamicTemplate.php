<?php
// +----------------------------------------------------------------------
// | likeshop开源商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop系列产品在gitee、github等公开渠道开源版本可免费商用，未经许可不能去除前后端官方版权标识
// |  likeshop系列产品收费版本务必购买商业授权，购买去版权授权后，方可去除前后端官方版权标识
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | likeshop团队版权所有并拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshop.cn.team
// +----------------------------------------------------------------------

namespace app\admin\validate;

use think\Db;
use think\Validate;

/**
 * 动态模板验证器
 * Class DynamicTemplate
 * @package app\admin\validate
 */
class DynamicTemplate extends Validate
{
    protected $rule = [
        'id'                  => 'require|integer|gt:0',
        'template_id'         => 'require|max:50|checkTemplateIdUnique',
        'title'               => 'require|max:255',
        'description'         => 'max:1000',
        'category'            => 'require|max:50',
        'tags'                => 'max:500',
        'thumbnail'           => 'max:500',
        'width'               => 'require|integer|gt:0|max:10000',
        'height'              => 'require|integer|gt:0|max:10000',
        'text_elements_count' => 'integer|egt:0',
        'param_count'         => 'integer|egt:0',
        'template_data'       => 'require',
        'status'              => 'in:0,1',
        'sort'                => 'integer|egt:0',
        
        // 参数配置相关
        'params'              => 'array',
        'params.*.param_name' => 'require|max:100',
        'params.*.param_label' => 'require|max:100',
        'params.*.param_type' => 'require|in:1,2,3,4,5,6',
        'params.*.max_length' => 'integer|gt:0|max:1000',
        'params.*.is_required' => 'in:0,1',
        
        // 批量生成相关
        'task_name'           => 'require|max:255',
        'parameters_list'     => 'require|array',
        'output_width'        => 'integer|gt:0|max:10000',
        'output_height'       => 'integer|gt:0|max:10000',
        'output_quality'      => 'float|between:0.1,1',
    ];

    protected $message = [
        'id.require'                    => 'ID不能为空',
        'id.integer'                    => 'ID必须为整数',
        'id.gt'                         => 'ID必须大于0',
        'template_id.require'           => '模板ID不能为空',
        'template_id.max'               => '模板ID长度不能超过50个字符',
        'template_id.checkTemplateIdUnique' => '模板ID已存在',
        'title.require'                 => '模板标题不能为空',
        'title.max'                     => '模板标题长度不能超过255个字符',
        'description.max'               => '模板描述长度不能超过1000个字符',
        'category.require'              => '模板分类不能为空',
        'category.max'                  => '模板分类长度不能超过50个字符',
        'tags.max'                      => '标签长度不能超过500个字符',
        'thumbnail.max'                 => '缩略图URL长度不能超过500个字符',
        'width.require'                 => '画布宽度不能为空',
        'width.integer'                 => '画布宽度必须为整数',
        'width.gt'                      => '画布宽度必须大于0',
        'width.max'                     => '画布宽度不能超过10000',
        'height.require'                => '画布高度不能为空',
        'height.integer'                => '画布高度必须为整数',
        'height.gt'                     => '画布高度必须大于0',
        'height.max'                    => '画布高度不能超过10000',
        'text_elements_count.integer'   => '文本元素数量必须为整数',
        'text_elements_count.egt'       => '文本元素数量不能小于0',
        'param_count.integer'           => '参数数量必须为整数',
        'param_count.egt'               => '参数数量不能小于0',
        'template_data.require'         => '模板数据不能为空',
        'status.in'                     => '状态值不正确',
        'sort.integer'                  => '排序必须为整数',
        'sort.egt'                      => '排序不能小于0',
        
        // 参数配置相关
        'params.array'                  => '参数配置必须为数组',
        'params.*.param_name.require'   => '参数名称不能为空',
        'params.*.param_name.max'       => '参数名称长度不能超过100个字符',
        'params.*.param_label.require'  => '参数标签不能为空',
        'params.*.param_label.max'      => '参数标签长度不能超过100个字符',
        'params.*.param_type.require'   => '参数类型不能为空',
        'params.*.param_type.in'        => '参数类型不正确',
        'params.*.max_length.integer'   => '最大长度必须为整数',
        'params.*.max_length.gt'        => '最大长度必须大于0',
        'params.*.max_length.max'       => '最大长度不能超过1000',
        'params.*.is_required.in'       => '是否必填值不正确',
        
        // 批量生成相关
        'task_name.require'             => '任务名称不能为空',
        'task_name.max'                 => '任务名称长度不能超过255个字符',
        'parameters_list.require'       => '参数列表不能为空',
        'parameters_list.array'         => '参数列表必须为数组',
        'output_width.integer'          => '输出宽度必须为整数',
        'output_width.gt'               => '输出宽度必须大于0',
        'output_width.max'              => '输出宽度不能超过10000',
        'output_height.integer'         => '输出高度必须为整数',
        'output_height.gt'              => '输出高度必须大于0',
        'output_height.max'             => '输出高度不能超过10000',
        'output_quality.float'          => '输出质量必须为数字',
        'output_quality.between'        => '输出质量必须在0.1-1之间',
    ];

    protected $scene = [
        'add'    => ['template_id', 'title', 'description', 'category', 'tags', 'thumbnail',
                     'width', 'height', 'text_elements_count', 'param_count', 'template_data',
                     'status', 'sort', 'params'],
        'edit'   => ['id', 'template_id', 'title', 'description', 'category', 'tags', 'thumbnail',
                     'width', 'height', 'text_elements_count', 'param_count', 'template_data',
                     'status', 'sort', 'params'],
        'del'    => ['id'],
        'status' => ['id', 'status'],
        'params' => ['id', 'params'],
        'batch'  => ['id', 'task_name', 'parameters_list', 'output_width', 'output_height', 'output_quality'],
    ];

    /**
     * 检查模板ID唯一性
     * @param $value
     * @param $rule
     * @param $data
     * @return bool|string
     */
    protected function checkTemplateIdUnique($value, $rule, $data)
    {
        $where = [['template_id', '=', $value], ['delete_time', 'null', '']];
        
        // 编辑时排除当前记录
        if (isset($data['id']) && $data['id']) {
            $where[] = ['id', '<>', $data['id']];
        }
        
        $exists = Db::name('dynamic_template')->where($where)->find();
        
        return $exists ? '模板ID已存在' : true;
    }

    /**
     * 添加场景
     * @return DynamicTemplate
     */
    protected function sceneAdd()
    {
        return $this->remove('id', true);
    }

    /**
     * 编辑场景
     * @return DynamicTemplate
     */
    protected function sceneEdit()
    {
        return $this->append('template_id', 'checkTemplateIdUnique');
    }

    /**
     * 删除场景
     * @return DynamicTemplate
     */
    protected function sceneDel()
    {
        return $this->only(['id']);
    }

    /**
     * 状态切换场景
     * @return DynamicTemplate
     */
    protected function sceneStatus()
    {
        return $this->only(['id', 'status']);
    }

    /**
     * 参数配置场景
     * @return DynamicTemplate
     */
    protected function sceneParams()
    {
        return $this->only(['id', 'params']);
    }

    /**
     * 批量生成场景
     * @return DynamicTemplate
     */
    protected function sceneBatch()
    {
        return $this->only(['id', 'task_name', 'parameters_list', 'output_width', 'output_height', 'output_quality']);
    }
}
