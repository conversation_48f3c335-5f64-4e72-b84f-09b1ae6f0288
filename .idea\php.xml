<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="PhpIncludePathManager">
    <include_path>
      <path value="$PROJECT_DIR$/vendor/overtrue/wechat" />
      <path value="$PROJECT_DIR$/vendor/overtrue/socialite" />
      <path value="$PROJECT_DIR$/vendor/danielstjules/stringy" />
      <path value="$PROJECT_DIR$/vendor/qcloud/cos-sdk-v5" />
      <path value="$PROJECT_DIR$/vendor/adbario/php-dot-notation" />
      <path value="$PROJECT_DIR$/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/vendor/symfony/service-contracts" />
      <path value="$PROJECT_DIR$/vendor/psr/cache" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-ctype" />
      <path value="$PROJECT_DIR$/vendor/psr/http-client" />
      <path value="$PROJECT_DIR$/vendor/pimple/pimple" />
      <path value="$PROJECT_DIR$/vendor/rmccue/requests" />
      <path value="$PROJECT_DIR$/vendor/symfony/event-dispatcher-contracts" />
      <path value="$PROJECT_DIR$/vendor/psr/http-message" />
      <path value="$PROJECT_DIR$/vendor/symfony/mime" />
      <path value="$PROJECT_DIR$/vendor/symfony/yaml" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-php73" />
      <path value="$PROJECT_DIR$/vendor/psr/log" />
      <path value="$PROJECT_DIR$/vendor/symfony/property-access" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-php72" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-intl-idn" />
      <path value="$PROJECT_DIR$/vendor/endroid/qr-code" />
      <path value="$PROJECT_DIR$/vendor/dragonmantank/cron-expression" />
      <path value="$PROJECT_DIR$/vendor/yly-openapi/yly-openapi-sdk" />
      <path value="$PROJECT_DIR$/vendor/monolog/monolog" />
      <path value="$PROJECT_DIR$/vendor/aliyuncs/oss-sdk-php" />
      <path value="$PROJECT_DIR$/vendor/mtdowling/jmespath.php" />
      <path value="$PROJECT_DIR$/vendor/bacon/bacon-qr-code" />
      <path value="$PROJECT_DIR$/vendor/topthink/think-installer" />
      <path value="$PROJECT_DIR$/vendor/topthink/think-image" />
      <path value="$PROJECT_DIR$/vendor/topthink/think-helper" />
      <path value="$PROJECT_DIR$/vendor/topthink/think-captcha" />
      <path value="$PROJECT_DIR$/vendor/clagiordano/weblibs-configmanager" />
      <path value="$PROJECT_DIR$/vendor/topthink/framework" />
      <path value="$PROJECT_DIR$/vendor/songshenzong/support" />
      <path value="$PROJECT_DIR$/vendor/easywechat-composer/easywechat-composer" />
      <path value="$PROJECT_DIR$/vendor/alibabacloud/client" />
      <path value="$PROJECT_DIR$/vendor/alibabacloud/tea" />
      <path value="$PROJECT_DIR$/vendor/alibabacloud/tea-fileform" />
      <path value="$PROJECT_DIR$/vendor/alipaysdk/easysdk" />
      <path value="$PROJECT_DIR$/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-mbstring" />
      <path value="$PROJECT_DIR$/vendor/symfony/deprecation-contracts" />
      <path value="$PROJECT_DIR$/vendor/symfony/cache-contracts" />
      <path value="$PROJECT_DIR$/vendor/symfony/http-foundation" />
      <path value="$PROJECT_DIR$/vendor/guzzlehttp/guzzle-services" />
      <path value="$PROJECT_DIR$/vendor/symfony/event-dispatcher" />
      <path value="$PROJECT_DIR$/vendor/guzzlehttp/uri-template" />
      <path value="$PROJECT_DIR$/vendor/symfony/options-resolver" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-intl-normalizer" />
      <path value="$PROJECT_DIR$/vendor/psr/http-factory" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-php80" />
      <path value="$PROJECT_DIR$/vendor/guzzlehttp/promises" />
      <path value="$PROJECT_DIR$/vendor/khanamiryan/qrcode-detector-decoder" />
      <path value="$PROJECT_DIR$/vendor/psr/container" />
      <path value="$PROJECT_DIR$/vendor/symfony/psr-http-message-bridge" />
      <path value="$PROJECT_DIR$/vendor/guzzlehttp/command" />
      <path value="$PROJECT_DIR$/vendor/symfony/cache" />
      <path value="$PROJECT_DIR$/vendor/guzzlehttp/psr7" />
      <path value="$PROJECT_DIR$/vendor/psr/simple-cache" />
      <path value="$PROJECT_DIR$/vendor/symfony/var-exporter" />
      <path value="$PROJECT_DIR$/vendor/guzzlehttp/guzzle" />
      <path value="$PROJECT_DIR$/vendor/composer" />
      <path value="$PROJECT_DIR$/vendor/tencentcloud/tencentcloud-sdk-php" />
      <path value="$PROJECT_DIR$/vendor/ralouphie/getallheaders" />
      <path value="$PROJECT_DIR$/vendor/myclabs/php-enum" />
      <path value="$PROJECT_DIR$/vendor/qiniu/php-sdk" />
    </include_path>
  </component>
  <component name="PhpProjectSharedConfiguration" php_language_level="7.0" />
</project>