<?xml version="1.0" encoding="UTF-8"?>
<module type="JAVA_MODULE" version="4">
  <component name="NewModuleRootManager" inherit-compiler-output="true">
    <exclude-output />
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/application" isTestSource="false" packagePrefix="app\" />
      <sourceFolder url="file://$MODULE_DIR$/thinkphp/library" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/thinkphp/tests" isTestSource="true" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/adbario/php-dot-notation" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/alibabacloud/client" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/alibabacloud/tea" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/alibabacloud/tea-fileform" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/alipaysdk/easysdk" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/aliyuncs/oss-sdk-php" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/bacon/bacon-qr-code" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/clagiordano/weblibs-configmanager" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/composer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/danielstjules/stringy" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/dragonmantank/cron-expression" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/easywechat-composer/easywechat-composer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/endroid/qr-code" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/guzzlehttp/command" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/guzzlehttp/guzzle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/guzzlehttp/guzzle-services" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/guzzlehttp/promises" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/guzzlehttp/psr7" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/guzzlehttp/uri-template" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/khanamiryan/qrcode-detector-decoder" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/monolog/monolog" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/mtdowling/jmespath.php" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/myclabs/php-enum" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/overtrue/socialite" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/overtrue/wechat" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/pimple/pimple" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/cache" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/container" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/event-dispatcher" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/http-client" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/http-factory" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/http-message" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/log" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/simple-cache" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/qcloud/cos-sdk-v5" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/qiniu/php-sdk" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/ralouphie/getallheaders" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/rmccue/requests" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/songshenzong/support" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/cache" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/cache-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/deprecation-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/event-dispatcher" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/event-dispatcher-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/http-foundation" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/mime" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/options-resolver" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-ctype" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-intl-idn" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-intl-normalizer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-mbstring" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-php72" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-php73" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-php80" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/property-access" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/psr-http-message-bridge" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/service-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/var-exporter" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/yaml" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/tencentcloud/tencentcloud-sdk-php" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/topthink/framework" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/topthink/think-captcha" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/topthink/think-helper" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/topthink/think-image" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/topthink/think-installer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/webmozart/assert" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/yly-openapi/yly-openapi-sdk" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
</module>